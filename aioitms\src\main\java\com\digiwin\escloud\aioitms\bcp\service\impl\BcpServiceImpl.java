package com.digiwin.escloud.aioitms.bcp.service.impl;

import com.digiwin.escloud.aioai.model.HeaderInfo;
import com.digiwin.escloud.aioai.model.ChatMessage;
import com.digiwin.escloud.aioitms.bcp.dao.BcpMapper;
import com.digiwin.escloud.aioitms.bcp.model.*;
import com.digiwin.escloud.aioitms.bcp.service.IBcpService;
import com.digiwin.escloud.aioitms.bcp.utils.IamUtils;
import com.digiwin.escloud.aioitms.bigdata.BigDataUtil;
import com.digiwin.escloud.aioitms.util.MessageUtil;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.feign.AioAiFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.apache.commons.lang.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.UnicastProcessor;
import reactor.core.scheduler.Schedulers;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.Arrays;
import java.util.List;


@Slf4j
@Service
public class BcpServiceImpl implements IBcpService, ParamCheckHelp {
    @Autowired
    BcpMapper bcpMapper;
    @Autowired
    MessageUtil messageUtil;
    @Autowired
    BigDataUtil bigDataUtil;
    @Autowired
    AioAiFeignClient aioAiFeignClient;
    @Autowired
    IamUtils iamUtils;

    @Resource(name = "invokeIndepthAIAgent")
    WebClient invokeIndepthAIAgent;

    private static final String BCP_ASSISTANT = "BCP_ASSISTANT";

    @Override
    public BaseResponse getData(String version, String lang) {

        // 查詢問卷資訊
        List<BcpSurveyData> bcpSurveyData = bcpMapper.selectBcpSurveyData(version);

        // 處理i18n
        processDataListLanguage(bcpSurveyData, lang);

        // 回傳
        return BaseResponse.ok(bcpSurveyData);
    }

    @Override
    public BaseResponse getResultBySurveyId(String surveyId, String lang) {

        // 取得自評數據
        BcpSurveyResult bcpSurveyResult = selectBcpSurveyResult(surveyId, lang);
        if (Objects.isNull(bcpSurveyResult)) {
            return BaseResponse.ok(new BcpSurveyResult());
        }

        // 回傳
        return BaseResponse.ok(bcpSurveyResult);
    }

    @Override
    public BaseResponse getResultList(BcpRequestParam param) {

        // 分頁查詢清單
        Page page = PageHelper.startPage(param.getPageNum(), param.getPageSize());
        bcpMapper.selectBcpSurveyRecord(param);
        PageInfo<BcpSurveyRecord> pageInfo = new PageInfo<BcpSurveyRecord>(page);

        // 將bcp分頁數據倒到回傳格式中
        BcpSurveyListRes res = new BcpSurveyListRes();
        BcpSurveyPageInfo bcpSurveyPageInfo = convert(pageInfo);

        // 取得額外欄位數據
        Optional.ofNullable(bcpMapper.getAccountResetCount()) // 已綁定
                .filter(IntegerUtil::isNotEmpty)
                .ifPresent(bcpSurveyPageInfo::setAccountResetCount);
        Optional.ofNullable(bcpMapper.getNonResetCount()) // 未綁定
                .filter(IntegerUtil::isNotEmpty)
                .ifPresent(bcpSurveyPageInfo::setNoneResetCount);
        Optional.ofNullable(bcpMapper.getCustomerList()) // 客戶清單
                .filter(CollectionUtil::isNotEmpty)
                .ifPresent(bcpSurveyPageInfo::setCustomerList);

        // 若eid有傳入，取得最新一筆紀錄，否則為null
        if (StringUtil.isNotEmpty(param.getEid())) {
            // 處理i18n
            Optional.ofNullable(bcpMapper.getLastResult(param.getEid()))
                    .ifPresent(lastResult -> {
                        processResultLanguage(lastResult, param.getLang());
                        res.setLast(lastResult);
                    });
        }

        // 寫回到回傳參數中
        res.setBcp(bcpSurveyPageInfo);

        return BaseResponse.ok(res);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse saveRecord(BcpSurveyRecordSave bcpSurveyRecordSave) {
        try {
            // 設置自評記錄-主檔 id
            String recordId = String.valueOf(SnowFlake.getInstance().newId());
            bcpSurveyRecordSave.setSurveyId(recordId);

            // 儲存自評記錄-主檔
            Optional.ofNullable(saveBcpSurveyRecord(bcpSurveyRecordSave))
                    .filter(tasksAffectedRow -> tasksAffectedRow > 0)
                    .orElseThrow(() -> new RuntimeException("bcp survey record save error"));
            // 儲存自評記錄-明細
            Optional.ofNullable(saveBcpSurveyDataRecord(bcpSurveyRecordSave))
                    .filter(tasksAffectedRow -> tasksAffectedRow > 0)
                    .orElseThrow(() -> new RuntimeException("bcp survey data record save error"));
            // 儲存自評記錄-結果
            Optional.ofNullable(saveBcpSurveyResultRecord(bcpSurveyRecordSave))
                    .filter(tasksAffectedRow -> tasksAffectedRow > 0)
                    .orElseThrow(() -> new RuntimeException("bcp survey result record save error"));

            return BaseResponse.ok(recordId);
        } catch (Exception e) {
            log.error("Save BCP Result Error:", e);
            return BaseResponse.error(ResponseCode.SAVE_BCP_RESULT_ERROR);
        }

    }

    @Override
    public Flux<ServerSentEvent<Object>> getAiSuggestion(String surveyId, String lang) {
        String eid = StringUtil.toString(RequestUtil.getHeaderEid());
        String sid = StringUtil.toString(RequestUtil.getHeaderSid());
        String token = RequestUtil.getHeaderToken();

        // 取得自評數據
        BcpSurveyResult bcpSurveyResult = selectBcpSurveyResult(surveyId, lang);
        if (Objects.isNull(bcpSurveyResult)) {
            return Flux.just(
                    ServerSentEvent.builder()
                            .event("error")
                            .data(BaseResponse.error(ResponseCode.GET_BCP_RESULT_ERROR))
                            .build()
            );
        }

        // 去掉不需要的欄位
        BcpAiSuggestionPayload bcpAiSuggestQuestionInfo = new BcpAiSuggestionPayload();
        BeanUtils.copyProperties(bcpSurveyResult, bcpAiSuggestQuestionInfo);

        // 處理 BCP 數據 JSON
        String question = buildQuestion(bcpAiSuggestQuestionInfo, eid, sid, token);

        return invokeIndepthAIAgent(surveyId, question, lang, eid, sid, token);
    }

    private String buildQuestion(BcpAiSuggestionPayload bcpAiSuggestQuestionInfo, String eid,  String sid, String token) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String bcpDataJsonString = objectMapper.writeValueAsString(bcpAiSuggestQuestionInfo);
            String headerInfo = objectMapper.writeValueAsString(new BcpAiSuggestionHeaderInfo(eid, sid, token));

            // 組合 question 欄位數據
            BcpAiSuggestionQuestion bcpAiSuggestionQuestion = new BcpAiSuggestionQuestion(bcpDataJsonString, headerInfo);

            return objectMapper.writeValueAsString(bcpAiSuggestionQuestion);
        } catch (JsonProcessingException e) {
            log.error("GetAiSuggestion WriteValueAsString Error", e);
            throw new RuntimeException(e);
        }
    }

    private static <T> BcpSurveyPageInfo convert(PageInfo<T> pageInfo) {
        BcpSurveyPageInfo result = new BcpSurveyPageInfo();
        BeanUtils.copyProperties(pageInfo, result);
        return result;
    }

    private void processResultLanguage(BcpSurveyResult bcpSurveyResult, String lang) {
        if (CollectionUtil.isNotEmpty(bcpSurveyResult.getSurvey())) {
            bcpSurveyResult.getSurvey().forEach(category -> {
                translateStringFields(category, lang);
            });
        }
    }

    private void processResultListLanguage(BcpSurveyResult bcpSurveyResult, String lang) {

        translateStringFields(bcpSurveyResult, lang);

        if (CollectionUtil.isNotEmpty(bcpSurveyResult.getSurvey())) {
            bcpSurveyResult.getSurvey().forEach(category -> {
                translateStringFields(category, lang);

                if (CollectionUtil.isNotEmpty(category.getTags())) {
                    category.getTags().forEach(tag -> translateStringFields(tag, lang));
                }
            });
        }
    }

    private void processDataListLanguage(List<BcpSurveyData> bcpSurveyData, String lang) {
        bcpSurveyData.forEach(data -> {
            translateStringFields(data, lang);

            if (CollectionUtil.isNotEmpty(data.getQuestions())) {
                data.getQuestions().forEach(question -> {
                    translateStringFields(question, lang);

                    if (CollectionUtil.isNotEmpty(question.getAnswers())) {
                        question.getAnswers().forEach(answer -> translateStringFields(answer, lang));
                    }
                });
            }
        });
    }

    private void translateStringFields(Object object, String lang) {
        Arrays.stream(object.getClass().getDeclaredFields())
                .filter(field -> field.getType() == String.class)
                .forEach(field -> {
                    field.setAccessible(true);
                    try {
                        String original = (String) field.get(object);
                        if (StringUtil.isNotEmpty(original)) {
                            String translated = messageUtil.noExceptionLogGet(original, lang);
                            field.set(object, translated);
                        }
                    } catch (IllegalAccessException e) {
                        log.warn("Translation failed for field: " + field.getName(), e);
                    }
                });
    }

    private Integer saveBcpSurveyRecord(BcpSurveyRecordSave bcpSurveyRecordSave) {
        String eid = bcpSurveyRecordSave.getEid();
        String serviceCode = bcpSurveyRecordSave.getServiceCode();
        // 取得客戶統編
        if (!eid.isEmpty() && !serviceCode.isEmpty()) {
            bcpSurveyRecordSave.setTaxId(getCustomerTaxId(eid));
        }

        // 儲存自評記錄-主檔
        bcpSurveyRecordSave.setEid(eid.isEmpty() ? null : eid);
        return bcpMapper.saveBcpSurveyRecord(bcpSurveyRecordSave);
    }
    // 查詢統編
    private String getCustomerTaxId(String eid) {
        String srQuery = String.format("SELECT COALESCE(NULLIF(aci.customerTaxId, ''), NULLIF(at.taxCode, ''), '') AS customerTaxId " +
                "FROM servicecloud.ACP_Customer_Info aci " +
                "FULL OUTER JOIN servicecloud.AiopsTenant at ON aci.eid = at.eid " +
                "WHERE (aci.eid = '%s' OR at.eid = '%s') AND COALESCE(NULLIF(aci.customerTaxId, ''), NULLIF(at.taxCode, ''), '') <> '' ", eid, eid);

        List<Map<String, Object>> data = bigDataUtil.srQuery(srQuery);

        return data.isEmpty() ? "" : ObjectUtils.toString(data.get(0).get("customerTaxId"), "");
    }

    private Integer saveBcpSurveyDataRecord(BcpSurveyRecordSave bcpSurveyRecordSave) {
        // 匯集answerId
        List<String> answerIdList = bcpSurveyRecordSave.getList().stream()
                .map(BcpSurveyDataRecord::getAnswerId).collect(Collectors.toList());

        // 計算"權重得分"及"答案得分"
        List<Map<String, Object>> sourceList = bcpMapper.getBcpSurveyScore(answerIdList);

        // 儲存自評記錄-明細
        bcpSurveyRecordSave.getList().forEach(answer -> {
            answer.setBsrId(bcpSurveyRecordSave.getSurveyId());
            sourceList.stream().filter(source ->
                            ObjectUtils.toString(source.get("answerId"), "").equals(answer.getAnswerId()))
                    .forEach(source -> {
                        answer.setWeightScore(Double.valueOf(ObjectUtils.toString(source.get("weightScore"))));
                        answer.setAnswerScore(Double.valueOf(ObjectUtils.toString(source.get("answerScore"))));
                    });
        });
        return bcpMapper.insertBcpSurveyDataRecord(bcpSurveyRecordSave.getList());
    }

    private Integer saveBcpSurveyResultRecord(BcpSurveyRecordSave bcpSurveyRecordSave){
        // 計算"類別得分"
        List<Map<String, Object>> categoryScoreList = bcpMapper.getBcpSurveyCategoryScore(bcpSurveyRecordSave.getSurveyId());

        // 儲存自評記錄-結果
        List<BcpSurveyResultRecord> bcpSurveyResultRecordList = new ArrayList<>();
        categoryScoreList.forEach(source -> {
            BcpSurveyResultRecord bcpSurveyResultRecord = new BcpSurveyResultRecord();
            bcpSurveyResultRecord.setBsrId(bcpSurveyRecordSave.getSurveyId());
            bcpSurveyResultRecord.setCategoryId(ObjectUtils.toString(source.get("categoryId"), ""));
            bcpSurveyResultRecord.setCategoryScore(Double.valueOf(ObjectUtils.toString(source.get("categoryScore"))));
            bcpSurveyResultRecordList.add(bcpSurveyResultRecord);
        });
        return bcpMapper.insertBcpSurveyResultRecord(bcpSurveyResultRecordList);
    }

    @Override
    public BaseResponse saveCustomerData(BcpSurveyRecord param) {
        // region 參數檢查
        Optional<BaseResponse> optResponse = checkParamIsEmpty(param.getSurveyId(), "surveyId");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        // endregion

        if (StringUtil.isEmpty(param.getEid()) || StringUtil.isEmpty(param.getServiceCode())) {
            // region 檢查訪客必填欄位
            List<String> params = Stream.of(
                            new AbstractMap.SimpleEntry<>("taxId", checkParamIsEmpty(param.getTaxId(), "taxId")),
                            new AbstractMap.SimpleEntry<>("companyName", checkParamIsEmpty(param.getCompanyName(), "companyName")),
                            new AbstractMap.SimpleEntry<>("userId", checkParamIsEmpty(param.getUserId(), "userId")),
                            new AbstractMap.SimpleEntry<>("userName", checkParamIsEmpty(param.getUserName(), "userName")))
                    .filter(entry -> entry.getValue().isPresent())
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(params)) {
                return BaseResponse.dynamicError(ResponseCode.PARAM_IS_EMPTY, String.join(", ", params));
            }
            // endregion

            // 未傳入eid或serviceCode，則使用userId查詢到的serviceCode做客戶留資更新
            param.setEid(null);
            param.setServiceCode(null);

            // 使用userId，查詢eid和serviceCode
            List<TenantInfo> tenantInfoList = bcpMapper.selectTenantInfo(param);

            //拼接serviceCode字串
            String serviceCode = tenantInfoList.stream()
                    .map(TenantInfo::getServiceCode) // 取出每個物件的 serviceCode
                    .map(code -> "'" + code + "'") // 每個加上單引號
                    .collect(Collectors.joining(","));
            String srQueryParam = String.format(
                    "select distinct customerServiceCode,customerTaxId,customerFullName from servicecloud.ACP_Customer_Info "
                            + "where customerServiceCode in (%s) and customerTaxId='%s' and customerFullName ='%s'",
                    serviceCode, param.getTaxId(), param.getCompanyName());
            // 查詢租戶資料是否存在
            List<Map<String, Object>> customerInfo = bigDataUtil.srQuery(srQueryParam);

            // 若租戶確實存在，且只有一個對應的租戶，則自動歸戶
            if(customerInfo.size() == 1){
                TenantInfo tenantInfo = tenantInfoList.stream().filter(t -> t.getServiceCode().equals(customerInfo.get(0).get("customerServiceCode"))).findFirst().get();
                param.setServiceCode(tenantInfo.getServiceCode());
                param.setEid(tenantInfo.getEid());
            }
        }

        if (StringUtil.isNotEmpty(param.getEid()) && StringUtil.isNotEmpty(param.getServiceCode()) && StringUtil.isEmpty(param.getTaxId())) {
            // 若同時傳入eid和serviceCode，則另外查詢統編寫入客戶留資
            // 客服端報告歸戶時不修改統編(有寫入統編時)
            String eid = param.getEid();
            param.setTaxId(getCustomerTaxId(eid));
        }


        bcpMapper.saveBcpSurveyRecord(param);
        return BaseResponse.ok();
    }

    private BcpSurveyResult selectBcpSurveyResult(String surveyId, String lang) {
        // 取得自評數據
        BcpSurveyResult bcpSurveyResult = bcpMapper.selectBcpSurveyResult(surveyId);
        if (Objects.isNull(bcpSurveyResult)) {
            return null;
        }

        // 處理i18n
        processResultListLanguage(bcpSurveyResult, lang);

        return bcpSurveyResult;
    }

    // 流式智能建議
    private Flux<ServerSentEvent<Object>> invokeIndepthAIAgent(String surveyId, String question, String lang, String eid, String sid, String token) {

        String requestId = this.generateRequestId();

        final ConcurrentHashMap<String, StringBuilder> messageBuilders = new ConcurrentHashMap<>();

        messageBuilders.put(requestId, new StringBuilder());

        // 創建保存操作，包裝為Mono
        Mono<List<String>> saveOperation = Mono.fromRunnable(() -> {
            String aiSuggestion = StringUtil.toString(messageBuilders.get(requestId));

            // 保存到record中
            bcpMapper.saveAiSuggestion(surveyId, aiSuggestion);
            messageBuilders.remove(requestId); // 清除暫存
        });
        saveOperation = saveOperation.doOnSuccess(v -> log.debug("消息處理和保存完成"));

        // 處理完整訊息
        UnicastProcessor<String> messageProcessor = UnicastProcessor.create();
        Mono<List<String>> messageProcessingFlux = messageProcessor
                .doOnNext(batch -> {
                    if (!batch.isEmpty()) {
                        String combinedMessage = String.join("", batch);
                        messageBuilders.get(requestId).append(combinedMessage);
                    }
                })
                .then(saveOperation)
                .doOnError(e -> {
                    log.error("消息处理过程中发生错误", e);
                });
        messageProcessingFlux.subscribe();

        // 組合bodyBcpAiSuggestionBody
        ChatMessage chatMssage = ChatMessage.builder()
                .agentId(BCP_ASSISTANT)
                .sessionId("")
                .question(question)
                .inputId(UuidUtil.NewId())
                .outputId(UuidUtil.NewId())
                .build();

        return invokeIndepthAIAgent.post()
                .header("eid", eid)
                .header("sid", sid)
                .header("token", token)
                .header("Accept-Language", lang)
                .bodyValue(chatMssage)
                .retrieve()
                .bodyToFlux(new ParameterizedTypeReference<ServerSentEvent<Object>>() {})
                .doOnNext(event -> {

                    Map<String, Object> flowRow = new HashMap<>();
                    if (event instanceof ServerSentEvent) {
                        ServerSentEvent<?> sse = (ServerSentEvent<?>) event;
                        flowRow = (Map<String, Object>) sse.data();
                    }
                    flowRow = Optional.ofNullable(flowRow).orElse(new HashMap<>());

                    String action = this.getReturnFlowValue("action", flowRow);
                    String message = this.getReturnFlowValue("message", flowRow);

                    // 將 action 為 answer 的 message 拋給 messageProcessor
                    if ("answer".equalsIgnoreCase(action)) {
                        messageProcessor.onNext(message);
                    }
                })
                .doOnComplete(messageProcessor::onComplete)
                .doOnError(error -> {
                    log.error("Stream error occurred for requestId: " + requestId, error);
                    messageProcessor.onError(error);
                })
                .subscribeOn(Schedulers.boundedElastic());
    }

    protected String getReturnFlowValue(String key, Map<String, Object> flowRow) {
        return StringUtil.toString(flowRow.get(key));
    }

    private String generateRequestId() {
        return "req_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }

    // [此功能未使用但保留]
    private void saveBcpAiSuggestion(BcpSurveyRecordSave bcpSurveyRecordSave) {
        // 非同步儲存智能建議 (非流式)
        CompletableFuture.runAsync(() -> {
            String recordId = bcpSurveyRecordSave.getSurveyId();

            // 取得自評數據
            BcpSurveyResult bcpSurveyResult = selectBcpSurveyResult(recordId, bcpSurveyRecordSave.getLang());
            if (Objects.isNull(bcpSurveyResult)) {
                return;
            }

            // 去掉不需要的欄位
            BcpAiSuggestionPayload bcpAiSuggestQuestionInfo = new BcpAiSuggestionPayload();
            BeanUtils.copyProperties(bcpSurveyResult, bcpAiSuggestQuestionInfo);

            // 處理 BCP 數據 JSON
            HeaderInfo chatHeaders = iamUtils.getChatAuthHeaders();
            String sid = chatHeaders.getSid();
            String eid = chatHeaders.getEid();
            String token = chatHeaders.getToken();

            String question = buildQuestion(bcpAiSuggestQuestionInfo, eid, sid, token);

            ResponseBase response = invokeIndepthAIAgentSync(question, bcpSurveyRecordSave.getLang(), eid, sid, token);

            log.info("bcp save ai suggestion response: {}", response);

            if (ResponseCode.SUCCESS.getCode().equals(response.getCode())) {
                Integer row = bcpMapper.saveAiSuggestion(recordId, Objects.toString(response.getData(), ""));
                if (row < 1) {
                    log.error("bcp save ai suggestion error");
                }
            }
        });
    }

    // [此功能未使用但保留]
    private ResponseBase invokeIndepthAIAgentSync(String question, String lang, String eid, String sid, String token) {
        // 非流式智能建議
        return aioAiFeignClient.invokeIndepthAIAgentSync(eid, sid, token, lang,
                ChatMessage.builder()
                        .agentId(BCP_ASSISTANT)
                        .sessionId("")
                        .question(question)
                        .inputId(UuidUtil.NewId())
                        .outputId(UuidUtil.NewId())
                        .build());
    }
}
