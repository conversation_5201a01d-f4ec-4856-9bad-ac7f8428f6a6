package com.digiwin.escloud.aiocmdb.assetmaintenancev2.service;

import cn.hutool.core.lang.Pair;
import com.digiwin.escloud.aiocmdb.asset.dao.AssetMaintenanceV2Mapper;
import com.digiwin.escloud.aiocmdb.asset.model.AssetCategoryCodingRuleSimple;
import com.digiwin.escloud.aiocmdb.asset.model.AssetRelatedCategoryClassification;
import com.digiwin.escloud.aiocmdb.asset.model.AssetRelatedCategoryClassificationTree;
import com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.model.AssetAttribute;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.model.AssetRelatedMap;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.model.AssociatedWithAssets;
import com.digiwin.escloud.aiocmdb.etl.service.IEtlService;
import com.digiwin.escloud.aiocmdb.maintenancerecord.service.IMrService;
import com.digiwin.escloud.aioitms.model.bigdata.StarRocksEntity;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.DateUtil;
import com.digiwin.escloud.common.util.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.digiwin.escloud.etl.model.EtlEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Service
public class AssetRelatedMapServiceImpl extends AssetRelatedMapBasic implements AssetRelatedMapService {
    @Autowired
    private BigDataUtil bigDataUtil;

    @Autowired
    private IMrService mrService;

    @Resource
    private IEtlService etlService;

    @Autowired
    private AssetMaintenanceV2Mapper assetMaintenanceV2Mapper;

    public Integer deleteRelatedAsset(String eid, long assetRelatedId) {
        String deleteSql = String.format(
                "delete from servicecloud.AssetsRelated where eid = '%s' and id = %d", eid, assetRelatedId);
        return bigDataUtil.srSave(deleteSql);
    }

    /**
     * 使用所提供的列表將相關的資產數據添加到數據處理系統中 {@link AssetRelatedMap}.
     * 將輸入列表轉換為兼容數據結構並執行UPSERT操作.
     *
     * @param assetRelatedMaps {@link AssetRelatedMap} 對象的列表，其中包含有關相關資產的詳細信息, 例如模型代碼，水槽名稱和資產標識符
     * @return 包含數據處理操作結果的地圖，包括潛在的成功或錯誤詳細信息
     */
    public Map<String, Object> addRelatedAsset(List<AssetRelatedMap> assetRelatedMaps) {

        // 計算 ID
        assetRelatedMaps.forEach(row -> {
            if (row.getId() <= 0) {
                row.setId(SnowFlake.getInstance().newId()); //產生雪花 id
            }
        });

        // 取得 etl_endgine.sinkFieldsJson 的設定
        String schemaName = "default";
        String sinkType = "starrocks";
        String tableName = "AssetsRelated";
        List<EtlEngine> etlList = this.getEtlEngineInfo(schemaName, sinkType, tableName);
        String sinkFieldsJson = etlList.get(0).getSinkFieldsJson();

        // 處理數據
//        List<LinkedHashMap<String, Object>> rows = MapUtil.convertToListLinkedHashMap(assetRelatedMaps);
        List<LinkedHashMap<String, Object>> rows = MapUtil.convertToListLinkedHashMap(sinkFieldsJson, assetRelatedMaps);
        rows.forEach(row -> {
            Optional.ofNullable(row.get("collectedTime"))
                    .filter(collectedTime -> !collectedTime.toString().isEmpty()) // 不為 null 才會執行
                    .orElseGet(
                            () -> row.put("collectedTime",
                                    DateUtil.getNowFormatString(DateUtil.DATE_T_TIME_MIL_FORMATTER))
                    );
            // 只要有異動, 就要更新時間
            row.put("flumeTimestamp", DateUtil.getNowFormatString(DateUtil.DATE_T_TIME_MIL_FORMATTER));
        });

        // 生成 StarRocksEntity
        StarRocksEntity starRocksEntity = getStarRocksEntity(rows);
        starRocksEntity.setDatabase(bigDataUtil.getSrDbName());
        starRocksEntity.setTable(tableName);

        return bigDataUtil.srStreamLoadThrowsException(starRocksEntity);
    }

    /**
     * 通過執行一系列操作來保存資產屬性，包括保存到HBase，
     * 檢索和處理數據以保存在水槽存儲庫中，並與Starrocks數據庫集成。
     *
     * @param assetAttribute 資產屬性對象，其中包含用於HBase，接收器和 Starrocks 操作的必要數據。
     *                       它包括諸如接收器字段的HBase模型代碼，行ID，目標值和JSON數據之類的屬性。
     * @return 來自 Starrocks 數據流操作的結果圖。該地圖通常包含有關操作狀態以及潛在的其他相關信息。
     * @throws RuntimeException 如果任何操作失敗了，例如無法檢索 sinkfieldsjson 數據或錯誤期間的錯誤
     *                          數據處理或保存。
     */
    public Pair<Map<String, Object>, List<LinkedHashMap<String, Object>>> saveAssetAttribute(String modelCode, AssetAttribute assetAttribute) {

        // save to Sr
        // 取出 sinkFieldsJson
//        String dataJson = assetAttribute.getSrJson();
//        JSONObject dataObject = JSONObject.parseObject(dataJson);
//        String tableName = dataObject.getString("tableName");
//        String schemaName = Optional.ofNullable(dataObject.getString("schemaName")).orElse("");
//        String sinkType = Optional.ofNullable(dataObject.getString("sinkType")).orElse("");
//        String sinkName = Optional.ofNullable(dataObject.getString("sinkName")).orElse("");

        String tableName = assetAttribute.getSrData().getTableName();
        String schemaName = assetAttribute.getSrData().getSchemaName();
        String sinkType = assetAttribute.getSrData().getSinkType();
        String dbName = assetAttribute.getSrData().getDbName();
        if (dbName == null || dbName.isEmpty()) {
            throw new RuntimeException("dbName is null or empty");
        }

        // 取得 etl_endgine.sinkFieldsJson 的設定
        List<EtlEngine> etlList = this.getEtlEngineInfo(schemaName, sinkType, tableName);
        String sinkFieldsJson = etlList.get(0).getSinkFieldsJson();

        // 生成 StarRocksEntity
        // 轉換 json 資料
//        List<LinkedHashMap<String, Object>> rows = bigDataUtil.extractMultipleDataFromJson(sinkFieldsJson, dataJson);
        List<LinkedHashMap<String, Object>> rows = MapUtil.convertToListLinkedHashMap(sinkFieldsJson, assetAttribute.getSrData().getData());

        //處理主鍵及其它資料的資料
        rows.forEach(row -> {
            // 處理 assetId
            Optional.ofNullable(row.get("assetId"))
                    .filter(assetId -> !assetId.toString().isEmpty())// 不為 null 才會執行
                    .filter(assetId -> {
                        if (assetId instanceof Long) {
                            return (long)assetId > 0;
                        } else if (assetId instanceof Integer) {
                            return (int)assetId > 0;
                        }
                        return false; //如果非數字, 全都要重算
                    })
                    .orElseGet(() -> row.put("assetId", SnowFlake.getInstance().newId()));

            // 處理資產編號
            //FIXME 如果資產編號有傳入, 要不要再重新檢查資產編號, 是不是己存 ?
            String eid = row.getOrDefault("eid", "").toString();
            Optional.ofNullable(row.get("assetCode"))
                    .filter(assetCode -> !assetCode.toString().isEmpty())// 不為 null 才會執行
                    .orElseGet(
                            () -> this.getNewAssetCode(modelCode, eid, assetAttribute)
                                    .map(newAssetCode -> row.put("assetCode", newAssetCode))
                                    .orElse("")
                    );
        
            Optional.ofNullable(row.get("collectedTime"))
                    .filter(collectedTime -> !collectedTime.toString().isEmpty()) // 不為 null 才會執行
                    .orElseGet(
                            () -> row.put("collectedTime",
                                    DateUtil.getNowFormatString(DateUtil.DATE_T_TIME_MIL_FORMATTER))
                    );

            // 只要有異動, 就要更新時間
            row.put("flumeTimestamp", DateUtil.getNowFormatString(DateUtil.DATE_T_TIME_MIL_FORMATTER));
        });

        // save to SR
        StarRocksEntity starRocksEntity = getStarRocksEntity(rows);
        starRocksEntity.setDatabase(dbName);
        starRocksEntity.setTable(tableName);
        Map<String, Object> result = bigDataUtil.srStreamLoadThrowsException(starRocksEntity);

        // save to hBase
        String hBaseId;
        if (assetAttribute.getHbaseRowId() <= 0) {
            // 目前只會有單筆存入, 不會一次存入多筆的情況
            hBaseId = rows.get(0).get("assetId").toString();
        } else {
            hBaseId = assetAttribute.getHbaseRowId().toString();
        }
        String hBaseModelCode = assetAttribute.getHbaseModelCode();
        String hBaseTargetValue = assetAttribute.getHbaseTargetValue();

        // 取得 hbase 裡面的相關訊息
        JSONObject jsonObj = JSONObject.parseObject(hBaseTargetValue);
        JSONObject fields = jsonObj.getJSONObject("field");
        JSONObject basicInfo = fields.getJSONObject("BasicInfo");

        // 處理 hbase 裡面的 assetId
        Long hBaseAssetId = Optional.ofNullable(basicInfo.getLong("assetId")).orElse(0L);
        if (hBaseAssetId <= 0) {
            basicInfo.put("assetId", hBaseId);
        }

        // 處理 hbase 裡面的 assetCode
        String hBaseAssetCode = Optional.ofNullable(basicInfo.getString("assetCode")).orElse("");
        if (hBaseAssetCode.isEmpty()) {
            hBaseAssetCode = Optional.ofNullable(rows.get(0).get("assetCode")).orElse("").toString();
            basicInfo.put("assetCode", hBaseAssetCode);
        }

        fields.put("BasicInfo", basicInfo);
        jsonObj.put("field", fields);
        hBaseTargetValue = jsonObj.toJSONString();

        // 取得要存 hbase 的 eid, 如果找不到, 就用 header 裡面的
        long eid = assetAttribute.getEid() <= 0 ? RequestUtil.getHeaderEid() : assetAttribute.getEid();

        // save to Hbase
        mrService.saveMrDetail(eid, hBaseModelCode, hBaseId, hBaseTargetValue);

        return Pair.of(result, rows);
    }

    /**
     * 取新的資產編號
     * @param modelCode
     * @param eid
     * @param assetAttribute
     * @return
     */
    public Optional<String> getNewAssetCode(String modelCode, String eid, AssetAttribute assetAttribute) {
        String sinkName = assetAttribute.getSrData().getTableName();

        if (eid == null || eid.isEmpty()) {
            throw new RuntimeException("Cal AssetCode found eid is null or empty");
        }

        // 取出編碼規則
        Supplier<List<AssetCategoryCodingRuleSimple>> assetCodeRuleFunc =
                () -> this.getAssetCategoryCodingRuleSimple(modelCode, sinkName);

        Function<List<AssetCategoryCodingRuleSimple>, Pair<String, Long>> curFlowNumFunc = codingRuleSimples -> {

            //要計算, 當前編碼的 正確編碼長度, 才能拿到當前編規則下, 所產生的資產編號
            String mainCode = codingRuleSimples.get(0).getMainCode();
            int ruleLen = codingRuleSimples.stream()
                    .map(row ->
                            Pair.of(row.getRuleNumber(), Optional.ofNullable(row.getRuleSettingValue()).orElse(""))
                    )
                    .mapToInt(ruleSettingValue -> {
                        switch (ruleSettingValue.getKey()) {
                            case TEXT:
                            case DATE:
                                return ruleSettingValue.getValue().length(); //text, date 都直接看長度
                            case SERIAL_NUMBER:
                                return Integer.parseInt(ruleSettingValue.getValue());
                        }
                        return 0;
                    })
                    .sum();
            int curFnLen = mainCode.length() + ruleLen;

            // 取出本次跟 rulekey 有關的 modelCode 跟 sinkName
            Map<String, Object> params = new HashMap<>();
            params.put("ruleKey", codingRuleSimples.get(0).getRuleKey());
            List<AssetCategoryCodingRuleSimple> ruleKeyGroupData = assetMaintenanceV2Mapper.selectAssetCodingRuleSetting(params);

            //取出當前跟 rulekey 有關的 sinkName
            String unionSql = ruleKeyGroupData.stream()
                    .map(AssetCategoryCodingRuleSimple::getSinkName)
                    .distinct()
                    .map(subSinkName -> String.format(
                            "select assetCode as curFn, collectedTime from servicecloud.%s " +
                                    "where eid = '%s' " +
                                    "and length(replace(assetCode, '-', '')) = %s ", // 要去除 '-' 才能取到正確的長度
                            subSinkName, eid, curFnLen))
                    .collect(Collectors.joining(" union all "));

            String sql = String.format("select tmp.curFn, tmp2.total from (\n" +
                    "    select * from (\n" +
                    "       %s\n" +
                    "    ) a\n" +
                    "    order by curFn desc, collectedTime desc limit 1\n" +
                    ") as tmp \n" +
                    "join (\n" +
                    "    select count(1) total from (\n" +
                    "        %s\n" +
                    "   ) a   \n" +
                    ") tmp2", unionSql, unionSql);
            List<Map<String, Object>> maxFnList = bigDataUtil.srQuery(sql);
            //取出當前資產規則的最大編號
            if (!maxFnList.isEmpty()) {
                String curFn = Optional.ofNullable(maxFnList.get(0).get("curFn")).orElse("").toString();
                long totalRows = Long.parseLong(
                        Optional.ofNullable(maxFnList.get(0).get("total")).orElse("0").toString()
                );
                return Pair.of(curFn, totalRows);
            }
            return Pair.of("", 0L);
        };

        // 取出編碼規則 groupLey
        Optional<String> ruleKeyOpt = this.getRuleKey(eid, modelCode, sinkName);
        if (!ruleKeyOpt.isPresent()) {
            return Optional.empty(); // 查找不到 ruleKey, 表示不需產生資產編號
        }

        //計算新的資產編號
        return this.getCacheAssetCode(
                eid, ruleKeyOpt.get(), assetCodeRuleFunc, curFlowNumFunc,0
        );
    }

    /**
     * 根據主要資產編號和處理其分類和詳細信息，與查詢相關的資產。
     *
     * @param primaryAssetId 用於查詢相關資產的主要資產編號。
     * @return AssetRealedCategoryClassification對象的列表，包含分類和分組的詳細信息相關資產。
     */
    public List<AssetRelatedCategoryClassificationTree> queryRelatedAssets(String eid, String primaryAssetId) {
        // step1. 從 关联资产 取出 关联资产資料
        String relatedAssetsSql = String.format(
                "select * from servicecloud.AssetsRelated where eid = '%s' and primaryAssetId = %s ", eid, primaryAssetId);
        List<Map<String, Object>> relatedAssets = bigDataUtil.srQuery(relatedAssetsSql);

        List<AssociatedWithAssets> associatedWithAssets = relatedAssets.stream()
                .map(row -> {
                    String assetRelatedId = Optional.ofNullable(row.get("id")).orElse("").toString();
                    String qEid = Optional.ofNullable(row.get("eid")).orElse("").toString();
                    String associatedAssetId = Optional.ofNullable(row.get("associatedAssetId")).orElse("").toString();
                    String associatedModelCode = Optional.ofNullable(row.get("associatedModelCode")).orElse("").toString();
                    String associatedSinkName = Optional.ofNullable(row.get("associatedSinkName")).orElse("").toString();
                    return new AssociatedWithAssets(
                            qEid, assetRelatedId, associatedAssetId, associatedModelCode, associatedSinkName);
                })
                .collect(Collectors.toList());

        if (associatedWithAssets.isEmpty()) {
            return new ArrayList<>();
        }

        // step2.1 要過濾重復 的 資產類別類no, 到 mysql 查
        List<String> modelCodes = associatedWithAssets.stream()
                .map(AssociatedWithAssets::getAssociatedModelCode)
                .distinct()
                .collect(Collectors.toList());
        List<String> sinkNames = associatedWithAssets.stream()
                .map(AssociatedWithAssets::getAssociatedSinkName)
                .distinct()
                .collect(Collectors.toList());

        Map<String, Object> params = new LinkedHashMap<>();
        params.put("modelCodes", modelCodes);
        params.put("sinkNames", sinkNames);
        List<AssetRelatedCategoryClassification> relatedClassifications =
                assetMaintenanceV2Mapper.selectAssetCategory(params);

        // step2.2 分組關聯的資產訊息
        associatedWithAssets.forEach(associatedWithAsset -> {
            relatedClassifications.forEach(relatedClassification -> {
                relatedClassification.groupRelateAssets(associatedWithAsset);
            });
        });

        // step3. 查詢相關資產的資料
        List<AssetRelatedCategoryClassification> data = relatedClassifications.stream()
                .peek(categoryClassification -> { // 實際操作的 logic
                    categoryClassification.getListAssociatedWithAssets().forEach(awa -> {
                        // step3.1. 從 关联资产 所設定的資產表( 用 关联资产编号 去查), 取出資料 (by 各表查詢)
                        String assetRelatedId = awa.getAssetRelatedId();
                        String qEid = awa.getEid();
                        String modelCode = awa.getAssociatedModelCode();
                        String relateTable = awa.getAssociatedSinkName();
                        String assetId = awa.getAssociatedAssetId();
                        String sql = String.format(
                                "select '%s' as assetRelatedId, main.* from servicecloud.%s main where eid = '%s' and assetId = '%s' ",
                                assetRelatedId, relateTable, qEid, assetId
                        );
                        List<Map<String, Object>> assetData = bigDataUtil.srQuery(sql);

                        // step4. 依取出的 資產類別類 分類資料
                        categoryClassification.addStoreData(modelCode, assetData);
                    });
                })
                .peek(categoryClassification -> {
                    //模擬 finally 的操作(fake)
                    categoryClassification.finalProcessCategoryClassification();
                    categoryClassification.clearUnnecessaryInfo(); // 清除不用外傳的資訊
                })
                .collect(Collectors.toList());

        // 只取出有有資料的
        data = data.stream()
                .filter(row -> row.getRelateStoreData() != null && !row.getRelateStoreData().isEmpty())
                .collect(Collectors.toList());
        if (data.isEmpty()) {
            return new ArrayList<>();
        }

        // 生成 tree 結構
        return this.getAssetClassificationTree(data);
    }

    private List<AssetRelatedCategoryClassificationTree> getAssetClassificationTree(
            List<AssetRelatedCategoryClassification> data) {

        // step0.創建所有節點
        List<AssetRelatedCategoryClassificationTree> allNodes = assetMaintenanceV2Mapper.selectAssetCategoryClassification();
        Map<Long, AssetRelatedCategoryClassificationTree> nodeMap = new HashMap<>();
        allNodes.forEach(row -> {
            row.setSubTree(new ArrayList<>());
            row.setTreeData(new ArrayList<>());
            nodeMap.put(row.getTreeId(), row);
        });

        //step1.添加 treeData
        data.forEach(row -> {
            Long key = row.getClassificationId();
            nodeMap.get(key).getTreeData().addAll(row.getRelateStoreData());
        });

        // step2.建立父子關係
        List<AssetRelatedCategoryClassificationTree> rootNodes = new ArrayList<>();
        for (AssetRelatedCategoryClassificationTree node : allNodes) {
            if (node.getTreeParentId() == null) {
                // 根節點
                rootNodes.add(node);
            } else {
                // 子節點
                AssetRelatedCategoryClassificationTree parent = nodeMap.get(node.getTreeParentId());
                if (parent != null) {
                    parent.getSubTree().add(node);
                } else {
                    // 如果找不到父節點，也將其視為根節點
                    rootNodes.add(node);
                }
            }
        }

        // step3.過濾沒有資料的節點
        return rootNodes.stream()
                .filter(this::hasDataInNodeOrChildren)
                .map(this::removeEmptySubNodes)
                .collect(Collectors.toList());
    }

    /**
     * 只檢查節點或其子節點是否有資料（不修改結構）
     */

    private boolean hasDataInNodeOrChildren(AssetRelatedCategoryClassificationTree node) {

        // 檢查當前節點是否有資料
        if (!node.getTreeData().isEmpty()) {
            return true;
        }

        // 檢查子節點是否有資料
        return node.getSubTree().stream()
                .anyMatch(this::hasDataInNodeOrChildren);
    }

    /**
     * 遞歸移除沒有資料的子節點（修改結構）
     */
    private AssetRelatedCategoryClassificationTree removeEmptySubNodes(AssetRelatedCategoryClassificationTree node) {
        if (node.getSubTree() != null && !node.getSubTree().isEmpty()) {
            List<AssetRelatedCategoryClassificationTree> filteredSubTree = node.getSubTree().stream()
                    .filter(this::hasDataInNodeOrChildren)  // 只保留有資料的子節點
                    .map(this::removeEmptySubNodes)         // 遞歸處理子節點
                    .collect(Collectors.toList());

            node.setSubTree(filteredSubTree);
        }
        return node;
    }

    /**
     * 取得  etl_engine 定義的 sr 欄位
     *
     * @param schemaName 数据库名称
     * @param sinkType sink类型
     * @param tableName sink表名或者topic名
     * @return 回傳etl_engine {@link EtlEngine} 的資料
     * @throws RuntimeException 如果ETL服務響應表示失敗或所需的數據丟失
     */
    private List<EtlEngine> getEtlEngineInfo(String schemaName, String sinkType, String tableName)  {
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("schemaName", schemaName);
        map.put("sinkType", sinkType);
        map.put("sinkName", tableName);
        BaseResponse etlInfo = etlService.getEtlListByMap(map);
        if (!etlInfo.checkIsSuccess()) {
            throw new RuntimeException(String.format("code:%s, errMsg:%s", etlInfo.getCode(), etlInfo.getErrMsg()));
        }
        if (etlInfo.getData() == null) {
            throw new RuntimeException("Not Found sinkFieldsJson data, err1");
        }
        List<EtlEngine> etlList = (List<EtlEngine>) etlInfo.getData();
        if (etlList.isEmpty()) {
            throw new RuntimeException("Not Found sinkFieldsJson data, err2");
        }
        return etlList;
    }

    private StarRocksEntity getStarRocksEntity(List<LinkedHashMap<String, Object>> rows) {
        StarRocksEntity starRocksEntity = new StarRocksEntity();

        // 設定數據
        starRocksEntity.setRows(rows);

        // 取出欄位名稱
        LinkedHashMap<String, Object> map = rows.stream().findFirst().get();
        String[] fieldArray = map.keySet().toArray(new String[0]);
        starRocksEntity.setFieldNames(fieldArray);

        // 設定其它參數
        Map<String, String> optProperties = new LinkedHashMap<>();
        optProperties.put("partial_update", "true");
        starRocksEntity.setOptProperties(optProperties);

        return starRocksEntity;
    }
}
